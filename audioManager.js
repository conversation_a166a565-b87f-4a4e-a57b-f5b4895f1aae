/**
 * 音频管理类
 * 负责游戏音效的播放和管理
 */

export class AudioManager {
    constructor() {
        // 音频上下文
        this.audioContext = null;
        this.masterGain = null;
        
        // 音频元素
        this.engineSound = null;
        this.brakeSound = null;
        this.collisionSound = null;
        
        // 音频状态
        this.isInitialized = false;
        this.isMuted = false;
        this.masterVolume = 0.7;
        
        // 引擎音效参数
        this.engineGain = null;
        this.engineFilter = null;
        this.engineSource = null;
        this.isEngineRunning = false;
        
        // 合成音效缓冲区
        this.audioBuffers = {};
    }
    
    async init() {
        try {
            // 创建音频上下文
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // 创建主音量控制
            this.masterGain = this.audioContext.createGain();
            this.masterGain.gain.value = this.masterVolume;
            this.masterGain.connect(this.audioContext.destination);
            
            // 获取HTML音频元素
            this.engineSound = document.getElementById('engine-sound');
            this.brakeSound = document.getElementById('brake-sound');
            this.collisionSound = document.getElementById('collision-sound');
            
            // 创建合成音效
            await this.createSyntheticSounds();
            
            // 设置引擎音效处理链
            this.setupEngineAudio();
            
            this.isInitialized = true;
            console.log('音频系统初始化完成');
            
        } catch (error) {
            console.warn('音频初始化失败，将使用静音模式:', error);
            this.isMuted = true;
        }
    }
    
    async createSyntheticSounds() {
        // 创建引擎声音缓冲区
        this.audioBuffers.engine = await this.createEngineBuffer();
        
        // 创建刹车声音缓冲区
        this.audioBuffers.brake = await this.createBrakeBuffer();
        
        // 创建碰撞声音缓冲区
        this.audioBuffers.collision = await this.createCollisionBuffer();
    }
    
    async createEngineBuffer() {
        const sampleRate = this.audioContext.sampleRate;
        const duration = 2; // 2秒循环
        const buffer = this.audioContext.createBuffer(1, sampleRate * duration, sampleRate);
        const data = buffer.getChannelData(0);
        
        // 生成引擎声音 - 低频噪声 + 周期性脉冲
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            
            // 基础低频噪声
            const noise = (Math.random() - 0.5) * 0.3;
            
            // 引擎脉冲 (模拟4缸引擎)
            const engineFreq = 30; // 基础频率
            const pulse = Math.sin(2 * Math.PI * engineFreq * t) * 0.4;
            const pulse2 = Math.sin(2 * Math.PI * engineFreq * 2 * t) * 0.2;
            
            // 低频隆隆声
            const rumble = Math.sin(2 * Math.PI * 15 * t) * 0.3;
            
            data[i] = (noise + pulse + pulse2 + rumble) * 0.25;
        }
        
        return buffer;
    }
    
    async createBrakeBuffer() {
        const sampleRate = this.audioContext.sampleRate;
        const duration = 1; // 1秒
        const buffer = this.audioContext.createBuffer(1, sampleRate * duration, sampleRate);
        const data = buffer.getChannelData(0);
        
        // 生成刹车声音 - 高频摩擦声
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            
            // 高频噪声
            const noise = (Math.random() - 0.5) * 0.8;
            
            // 摩擦声调制
            const friction = Math.sin(2 * Math.PI * 200 * t) * 0.3;
            const friction2 = Math.sin(2 * Math.PI * 150 * t) * 0.2;
            
            // 衰减包络
            const envelope = Math.exp(-t * 2);
            
            data[i] = (noise + friction + friction2) * envelope * 0.4;
        }
        
        return buffer;
    }
    
    async createCollisionBuffer() {
        const sampleRate = this.audioContext.sampleRate;
        const duration = 0.5; // 0.5秒
        const buffer = this.audioContext.createBuffer(1, sampleRate * duration, sampleRate);
        const data = buffer.getChannelData(0);
        
        // 生成碰撞声音 - 冲击声
        for (let i = 0; i < data.length; i++) {
            const t = i / sampleRate;
            
            // 白噪声
            const noise = (Math.random() - 0.5) * 1.0;
            
            // 冲击波
            const impact = Math.sin(2 * Math.PI * 80 * t) * 0.5;
            
            // 快速衰减
            const envelope = Math.exp(-t * 8);
            
            data[i] = (noise + impact) * envelope * 0.6;
        }
        
        return buffer;
    }
    
    setupEngineAudio() {
        if (!this.audioContext || !this.audioBuffers.engine) return;
        
        // 创建引擎音效处理链
        this.engineGain = this.audioContext.createGain();
        this.engineGain.gain.value = 0;
        
        // 创建滤波器用于音调变化
        this.engineFilter = this.audioContext.createBiquadFilter();
        this.engineFilter.type = 'lowpass';
        this.engineFilter.frequency.value = 800;
        this.engineFilter.Q.value = 1;
        
        // 连接音频链
        this.engineGain.connect(this.engineFilter);
        this.engineFilter.connect(this.masterGain);
    }
    
    playEngine() {
        if (!this.isInitialized || this.isMuted || this.isEngineRunning) return;
        
        try {
            // 恢复音频上下文（用户交互后）
            if (this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }
            
            // 创建音频源
            this.engineSource = this.audioContext.createBufferSource();
            this.engineSource.buffer = this.audioBuffers.engine;
            this.engineSource.loop = true;
            this.engineSource.connect(this.engineGain);
            
            // 开始播放
            this.engineSource.start();
            this.isEngineRunning = true;
            
            // 淡入效果
            this.engineGain.gain.setValueAtTime(0, this.audioContext.currentTime);
            this.engineGain.gain.linearRampToValueAtTime(0.3, this.audioContext.currentTime + 0.5);
            
        } catch (error) {
            console.warn('引擎音效播放失败:', error);
        }
    }
    
    stopEngine() {
        if (!this.isEngineRunning || !this.engineSource) return;
        
        try {
            // 淡出效果
            this.engineGain.gain.setValueAtTime(this.engineGain.gain.value, this.audioContext.currentTime);
            this.engineGain.gain.linearRampToValueAtTime(0, this.audioContext.currentTime + 0.3);
            
            // 停止播放
            setTimeout(() => {
                if (this.engineSource) {
                    this.engineSource.stop();
                    this.engineSource = null;
                    this.isEngineRunning = false;
                }
            }, 300);
            
        } catch (error) {
            console.warn('引擎音效停止失败:', error);
        }
    }
    
    pauseEngine() {
        if (this.isEngineRunning && this.engineGain) {
            this.engineGain.gain.setValueAtTime(this.engineGain.gain.value, this.audioContext.currentTime);
            this.engineGain.gain.linearRampToValueAtTime(0.1, this.audioContext.currentTime + 0.2);
        }
    }
    
    resumeEngine() {
        if (this.isEngineRunning && this.engineGain) {
            this.engineGain.gain.setValueAtTime(this.engineGain.gain.value, this.audioContext.currentTime);
            this.engineGain.gain.linearRampToValueAtTime(0.3, this.audioContext.currentTime + 0.2);
        }
    }
    
    updateEngine(speed, isAccelerating) {
        if (!this.isEngineRunning || !this.engineGain || !this.engineFilter) return;
        
        // 根据速度调整音量和音调
        const normalizedSpeed = Math.min(speed / 100, 1); // 假设最大速度100km/h
        const baseVolume = 0.3;
        const volume = baseVolume + (normalizedSpeed * 0.4);
        
        // 根据加速状态调整音量
        const targetVolume = isAccelerating ? volume * 1.2 : volume;
        
        // 平滑调整音量
        this.engineGain.gain.setValueAtTime(this.engineGain.gain.value, this.audioContext.currentTime);
        this.engineGain.gain.linearRampToValueAtTime(targetVolume, this.audioContext.currentTime + 0.1);
        
        // 根据速度调整滤波器频率（音调）
        const baseFreq = 800;
        const targetFreq = baseFreq + (normalizedSpeed * 1200);
        
        this.engineFilter.frequency.setValueAtTime(this.engineFilter.frequency.value, this.audioContext.currentTime);
        this.engineFilter.frequency.linearRampToValueAtTime(targetFreq, this.audioContext.currentTime + 0.1);
    }
    
    playBrake() {
        if (!this.isInitialized || this.isMuted) return;
        
        this.playBuffer(this.audioBuffers.brake, 0.5);
    }
    
    playCollision() {
        if (!this.isInitialized || this.isMuted) return;
        
        this.playBuffer(this.audioBuffers.collision, 0.8);
    }
    
    playBuffer(buffer, volume = 0.5) {
        if (!buffer || !this.audioContext) return;
        
        try {
            const source = this.audioContext.createBufferSource();
            const gain = this.audioContext.createGain();
            
            source.buffer = buffer;
            gain.gain.value = volume;
            
            source.connect(gain);
            gain.connect(this.masterGain);
            
            source.start();
            
        } catch (error) {
            console.warn('音效播放失败:', error);
        }
    }
    
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        if (this.masterGain) {
            this.masterGain.gain.value = this.masterVolume;
        }
    }
    
    toggleMute() {
        this.isMuted = !this.isMuted;
        if (this.masterGain) {
            this.masterGain.gain.value = this.isMuted ? 0 : this.masterVolume;
        }
    }
    
    stopAll() {
        this.stopEngine();
    }
    
    dispose() {
        this.stopAll();
        
        if (this.audioContext) {
            this.audioContext.close();
        }
    }
}
