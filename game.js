// 3D 黑白线条赛车 - Three.js + WebAudio 最小实现
// 视觉：纯黑白、线框；控制：左右转向、加减速；音效：合成引擎声、碰撞声

import * as THREE from "https://unpkg.com/three@0.160.0/build/three.module.js";
import { AudioManager } from "./audioManager.js";

const canvas = document.getElementById("scene");
const hudSpeed = document.getElementById("speed");
const hudScore = document.getElementById("score");
const overlay = document.getElementById("overlay");
const overlayHint = document.getElementById("overlay-hint");
const flash = document.getElementById("flash");

// 基础场景
const renderer = new THREE.WebGLRenderer({ canvas, antialias: true, alpha: false });
// 降低像素比以提升帧率（可用 Q 键切换）
let hiQuality = false;
function applyQuality() {
  const ratio = hiQuality ? Math.min(devicePixelRatio, 2) : 1;
  renderer.setPixelRatio(ratio);
  renderer.setSize(innerWidth, innerHeight);
}
applyQuality();
renderer.setSize(innerWidth, innerHeight);
renderer.setClearColor(0x000000, 1);

const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(70, innerWidth / innerHeight, 0.1, 1000);
scene.add(camera);

// 线框材质（黑底白线）
const lineMat = new THREE.LineBasicMaterial({ color: 0xffffff, linewidth: 1 });

// 赛道：用重复的线框条带模拟
const TRACK_LEN = 5000;
const TRACK_WIDTH = 10;
const SEG_LEN = 4;
const laneLines = new THREE.Group();
scene.add(laneLines);

function createLaneSegment(z) {
  const g = new THREE.BufferGeometry();
  const hw = TRACK_WIDTH / 2;
  const pts = new Float32Array([
    -hw, 0, z,   hw, 0, z,      // 横线
    -hw, 0, z+SEG_LEN, hw, 0, z+SEG_LEN,
  ]);
  g.setAttribute("position", new THREE.BufferAttribute(pts, 3));
  const line = new THREE.LineSegments(g, lineMat);
  return line;
}

for (let z = 0; z < TRACK_LEN; z += SEG_LEN*2) laneLines.add(createLaneSegment(-z));

// 边界立柱
const posts = new THREE.Group();
scene.add(posts);
for (let z = 0; z < TRACK_LEN; z += 20) {
  const hw = TRACK_WIDTH/2 + 0.3;
  const g = new THREE.BufferGeometry();
  const h = 1.2;
  const pts = new Float32Array([
    -hw, 0, -z, -hw, h, -z,
    hw, 0, -z,  hw,  h, -z,
  ]);
  g.setAttribute("position", new THREE.BufferAttribute(pts, 3));
  posts.add(new THREE.LineSegments(g, lineMat));
}

// 玩家车辆（线框三角棱）
const ship = new THREE.Group();
{
  const g = new THREE.BufferGeometry();
  const s = 0.6, l = 1.2, h = 0.4;
  const P = new Float32Array([
    0, h, 0,   -s, 0, -l,   s, 0, -l, // 顶面三角
    -s, 0, -l,  s, 0, -l,   0, 0, l,  // 底面连线
    -s, 0, -l,  0, 0, l,    0, h, 0,  // 侧1
    s, 0, -l,   0, 0, l,    0, h, 0,  // 侧2
  ]);
  g.setAttribute("position", new THREE.BufferAttribute(P, 3));
  ship.add(new THREE.LineSegments(g, lineMat));
}
scene.add(ship);
// 车辆包围盒半尺寸（近似）
const shipHalfX = 0.6; // 与构型 s 一致
const shipHalfZ = 1.0; // 稍小于 l 以获得更自然的判定

// 障碍（随机生成线框方柱）
const obstacles = new THREE.Group();
scene.add(obstacles);
function makeBoxWire(w, h, d, z) {
  const hw=w/2, hh=h/2, hd=d/2;
  const pts = [
    // 下方矩形
    [-hw,-hh,-hd], [ hw,-hh,-hd], [ hw,-hh, hd], [ -hw,-hh, hd], [ -hw,-hh,-hd],
    // 上方矩形
    [-hw, hh,-hd], [ hw, hh,-hd], [ hw, hh, hd], [ -hw, hh, hd], [ -hw, hh,-hd],
    // 竖线
    [-hw,-hh,-hd], [-hw, hh,-hd],
    [ hw,-hh,-hd], [ hw, hh,-hd],
    [ hw,-hh, hd], [ hw, hh, hd],
    [ -hw,-hh, hd], [ -hw, hh, hd]
  ];
  const arr = new Float32Array(pts.flat());
  const g = new THREE.BufferGeometry();
  g.setAttribute("position", new THREE.BufferAttribute(arr, 3));
  const m = new THREE.Line(g, lineMat);
  const group = new THREE.Group();
  group.add(m);
  group.position.z = z;
  // 存储障碍的半宽半深用于碰撞
  group.userData.hx = hw;
  group.userData.hz = hd;
  return group;
}

function spawnObstacles() {
  obstacles.clear();
  for (let z = -30; z > -TRACK_LEN+50; z -= 25) {
    if (Math.random() < 0.6) {
      const w = 0.8 + Math.random()*1.5;
      const h = 0.6 + Math.random()*1.2;
      const d = 0.8 + Math.random()*1.2;
      const box = makeBoxWire(w, h, d, z);
      box.position.x = (Math.random()*2 - 1) * (TRACK_WIDTH/2 - 1);
      obstacles.add(box);
    }
  }
}
spawnObstacles();

// 摄影机跟随
let camOffset = new THREE.Vector3(0, 2.0, 4.5);

// 控制状态
const keys = new Set();
window.addEventListener("keydown", (e)=>{
  // 防止重复触发
  if (!keys.has(e.code)) keys.add(e.code);
  if (e.code === "Space") {
    if (!running) { resetGame(); }
    togglePause();
  }
  if (e.code === "KeyM") toggleMute();
  if (e.code === "KeyQ") { hiQuality = !hiQuality; applyQuality(); }
});
window.addEventListener("keyup", (e)=> keys.delete(e.code));

let speed = 0;         // 前进速度
let targetSpeed = 10;  // 目标巡航
let steering = 0;      // 转向
let score = 0;
let running = false;
let lastT = performance.now();

// 音频管理器
const audioManager = new AudioManager();
let lastBrakeTime = 0;
async function initAudio() {
  await audioManager.init();
}

function toggleMute() {
  audioManager.toggleMute();
}

function setEngineSound(speed, isAccelerating = false) {
  audioManager.updateEngine(speed, isAccelerating);
}

function impactSound() {
  audioManager.playCollision();
}

function resetGame() {
  // 重置状态
  speed = 0; targetSpeed = 10; steering = 0; score = 0;
  ship.position.set(0, 0, 0);
  laneLines.position.z = 0; posts.position.z = 0; obstacles.position.z = 0;
  spawnObstacles();
}

function togglePause() {
  running = !running;
  overlay.style.display = running ? "none" : "grid";
  if (running) {
    overlayHint.textContent = "按空格暂停 · M 静音 · Q 画质";
    initAudio();
    audioManager.playEngine();
    lastT = performance.now();
    requestAnimationFrame(loop);
  } else {
    audioManager.pauseEngine();
  }
}

// 碰撞检测（AABB 简化判断）
function checkCollision() {
  const sx = ship.position.x, sz = ship.position.z;
  const parentZ = obstacles.position.z;
  for (const b of obstacles.children) {
    const bx = b.position.x; // 父组只沿 z 移动
    const bz = b.position.z + parentZ;
    const hx = b.userData?.hx ?? 0.6;
    const hz = b.userData?.hz ?? 0.6;
    const dx = Math.abs(bx - sx);
    const dz = Math.abs(bz - sz);
    if (dx < (hx + shipHalfX) && dz < (hz + shipHalfZ)) {
      return true;
    }
  }
  return false;
}

// 自适应
function onResize() {
  applyQuality();
  camera.aspect = innerWidth/innerHeight;
  camera.updateProjectionMatrix();
}
addEventListener("resize", onResize);

// 初始相机位置
const tmpVec3 = new THREE.Vector3();
camera.position.set(0, camOffset.y, camOffset.z);

// 主循环
function loop(now) {
  if (!running) return;
  const dt = Math.min(0.033, (now - lastT) / 1000);
  lastT = now;

  // 控制
  const accel = keys.has("ArrowUp") ? 22 : keys.has("ArrowDown") ? -18 : 0;
  const isAccelerating = keys.has("ArrowUp");
  const isBraking = keys.has("ArrowDown");
  targetSpeed = THREE.MathUtils.clamp(targetSpeed + accel*dt, 0, 40);
  speed += (targetSpeed - speed) * 0.9 * dt;

  // 播放刹车音效（避免频繁触发）
  if (isBraking && speed > 5 && (now - lastBrakeTime) > 500) {
    audioManager.playBrake();
    lastBrakeTime = now;
  }

  const steerTarget = (keys.has("ArrowLeft")? -1 : 0) + (keys.has("ArrowRight")? 1 : 0);
  steering += (steerTarget - steering) * 10 * dt;
  ship.position.x = THREE.MathUtils.clamp(ship.position.x + steering * 5 * dt, -TRACK_WIDTH/2 + 0.8, TRACK_WIDTH/2 - 0.8);

  // 前进（世界往后移）
  laneLines.position.z += speed * dt;
  posts.position.z += speed * dt;
  obstacles.position.z += speed * dt;
  score += speed * dt * 2;

  // 复用赛道
  if (laneLines.position.z > SEG_LEN*2) laneLines.position.z = 0;
  if (posts.position.z > 20) posts.position.z = 0;

  // 碰撞
  if (checkCollision()) {
    impactSound();
    flash.style.opacity = 0.85;
    setTimeout(()=> flash.style.opacity = 0, 40);

    // Game Over
    running = false;
    overlay.style.display = "grid";
    overlayHint.textContent = `Game Over · 得分 ${Math.floor(score)} · 空格重新开始`;
    audioManager.stopEngine();
  }

  // 相机跟随
  // 复用目标向量避免分配
  camera.position.lerp(tmpVec3.set(ship.position.x*0.6, camOffset.y, camOffset.z), 0.12);
  camera.lookAt(ship.position.x, 0.2, -2);

  // HUD
  hudSpeed.textContent = String((speed).toFixed(1));
  hudScore.textContent = String(Math.floor(score));

  // 引擎声
  setEngineSound(speed, isAccelerating);

  renderer.render(scene, camera);
  requestAnimationFrame(loop);
}

// 初次渲染，展示封面
overlayHint.textContent = "按空格开始 · M 静音 · Q 画质";
renderer.render(scene, camera);

