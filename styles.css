/* 黑白、线条、留白、等宽 */
:root {
  color-scheme: light dark;
  --bg: #000;
  --fg: #fff;
  --accent: #fff;
}

@font-face {
  font-family: "SF Mono";
  src: local("SF Mono"), local("SFMono-Regular");
  font-display: swap;
}

html, body {
  margin: 0;
  height: 100%;
  background: var(--bg);
  color: var(--fg);
  font-family: "SF Mono", ui-monospace, Menlo, Consolas, monospace;
}

#scene {
  position: fixed;
  inset: 0;
  width: 100vw;
  height: 100vh;
  display: block;
  z-index: 0;
}

#hud {
  position: fixed;
  top: 0; left: 0; right: 0;
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  pointer-events: none;
  z-index: 10;
}
.hud-row {
  display: flex; justify-content: space-between;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #222;
  padding-bottom: 6px;
}
.hud-row.small { border-bottom: none; opacity: 0.6; font-size: 12px; }

#overlay {
  position: fixed; inset:0; display:grid; place-items:center;
  background: radial-gradient(transparent, rgba(255,255,255,0.02));
  color: var(--fg);
  z-index: 20;
}
#overlay .box {
  border: 1px solid var(--fg);
  padding: 24px 28px;
  max-width: 520px;
  text-align: center;
  background: rgba(0,0,0,0.35);
  backdrop-filter: blur(4px);
}
#overlay h1 { margin: 0 0 8px; font-weight: 600; }
#overlay p { margin: 4px 0; }
#overlay .mono { letter-spacing: 1px; }

#flash {
  position: fixed; inset: 0; pointer-events: none;
  background: var(--fg);
  opacity: 0; transition: opacity 120ms ease;
  z-index: 30;
}

/* 可读性与克制 */
* { box-sizing: border-box; }


